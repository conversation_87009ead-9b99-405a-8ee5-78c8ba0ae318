from typing import List, Optional

from pydantic import Field

from core.schemas import CustomModel
from schemas.confirmed_data import ConfirmedData
from schemas.extracted_data import AggregatedData


class CombinedExtractedDataResponse(CustomModel):
    """
    Combined schema for extracted and aggregated data.
    """

    confirmed_data: ConfirmedData = Field(...)

    # Fields from AggregatedData, excluding those already in ConfirmedData
    # and those explicitly excluded by the user.
    business_issues: Optional[str] = Field(default=None)
    scope_approach: Optional[str] = Field(default=None)
    value_delivered: Optional[str] = Field(default=None)
    engagement_summary: Optional[str] = Field(default=None)
    one_line_description: Optional[str] = Field(default=None)
    client_references: Optional[str] = Field(default=None)
    client_name_sharing: Optional[str] = Field(default=None)
    client_industry: List[str] = Field(default_factory=list)
    engagement_dates: List[str] = Field(default_factory=list)
    engagement_locations: List[str] = Field(default_factory=list)
    engagement_fee_display: Optional[str] = Field(default=None)
    client_services: List[str] = Field(default_factory=list)
    source_of_work: Optional[str] = Field(default=None)
    qual_usage: Optional[str] = Field(default=None)
    team_roles: Optional[str] = Field(default=None)
    approver: Optional[str] = Field(default=None)
    dash_activity_id: Optional[int] = Field(default=None)

    @classmethod
    def from_confirmed_and_aggregated_data(
        cls, confirmed_data: ConfirmedData, aggregated_data: AggregatedData, dash_activity_id: Optional[int] = None
    ) -> 'CombinedExtractedDataResponse':
        combined_fields = {
            'confirmed_data': confirmed_data.model_dump(),
            'dash_activity_id': dash_activity_id,
        }

        excluded_aggregated_fields = {
            'client_name',
            'ldmf_country',
            'date_intervals',
            'date_intervals_original',
            'objective_and_scope',
            'outcomes',
            'more_than_two_dates',
        }

        for field_name, value in aggregated_data.model_dump().items():
            if field_name not in excluded_aggregated_fields:
                combined_fields[field_name] = value

        return cls.model_validate(combined_fields)
