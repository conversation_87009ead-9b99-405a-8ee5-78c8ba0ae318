import logging
from uuid import UUI<PERSON>

from fastapi import APIRouter, status

from constants.operation_ids import operation_ids
from dependencies import ConversationServiceDep, ExtractedDataServiceDep
from schemas.combined_extracted_data import CombinedExtractedDataResponse


__all__ = ['router']


logger = logging.getLogger(__name__)

router = APIRouter(prefix='/extracted-data-summary')


@router.get(
    '/{conversation_id}',
    operation_id=operation_ids.extracted_data_summary.GET,
    response_model=CombinedExtractedDataResponse,
    status_code=status.HTTP_200_OK,
)
async def get_combined_extracted_data(
    conversation_id: UUID,
    conversation_service: ConversationServiceDep,
    extracted_data_service: ExtractedDataServiceDep,
) -> CombinedExtractedDataResponse:
    """
    Get combined extracted and aggregated data for a specific conversation.
    """
    conversation = await conversation_service.get(conversation_id)
    confirmed_data = await conversation_service.get_confirmed_data(conversation_id)
    aggregated_data = await extracted_data_service.aggregate_data(conversation_id)

    return CombinedExtractedDataResponse.from_confirmed_and_aggregated_data(
        confirmed_data=confirmed_data,
        aggregated_data=aggregated_data,
        dash_activity_id=conversation.dash_activity_id,
    )
